import coremltools as ct
import torch
import torch.nn as nn
import numpy as np
from pathlib import Path
import json

class CoreMLAnalyzer:
    def __init__(self, coreml_path):
        self.coreml_path = coreml_path
        self.model = ct.models.MLModel(coreml_path)
        self.spec = self.model.get_spec()
        
    def analyze_model(self):
        """分析Core ML模型的详细结构"""
        print(f"分析模型: {self.coreml_path}")
        print("=" * 50)
        
        # 基本信息
        print("模型基本信息:")
        print(f"- 模型类型: {self.spec.WhichOneof('Type')}")
        print(f"- 规格版本: {self.spec.specificationVersion}")
        
        # 输入信息
        print("\n输入信息:")
        for i, input_desc in enumerate(self.spec.description.input):
            print(f"输入 {i+1}:")
            print(f"  - 名称: {input_desc.name}")
            print(f"  - 类型: {input_desc.type.WhichOneof('Type')}")
            
            if input_desc.type.HasField('multiArrayType'):
                array_type = input_desc.type.multiArrayType
                print(f"  - 形状: {list(array_type.shape)}")
                print(f"  - 数据类型: {array_type.dataType}")
        
        # 输出信息
        print("\n输出信息:")
        for i, output_desc in enumerate(self.spec.description.output):
            print(f"输出 {i+1}:")
            print(f"  - 名称: {output_desc.name}")
            print(f"  - 类型: {output_desc.type.WhichOneof('Type')}")
            
            if output_desc.type.HasField('multiArrayType'):
                array_type = output_desc.type.multiArrayType
                print(f"  - 形状: {list(array_type.shape)}")
                print(f"  - 数据类型: {array_type.dataType}")
        
        # 神经网络层信息
        if self.spec.HasField('neuralNetwork'):
            self.analyze_neural_network()
        elif self.spec.HasField('neuralNetworkClassifier'):
            self.analyze_neural_network_classifier()
        elif self.spec.HasField('pipelineClassifier'):
            self.analyze_pipeline_classifier()
        elif self.spec.HasField('pipeline'):
            self.analyze_pipeline()

        return self.extract_architecture_info()
    
    def analyze_neural_network(self):
        """分析神经网络结构"""
        print("\n神经网络层结构:")
        nn_spec = self.spec.neuralNetwork
        
        for i, layer in enumerate(nn_spec.layers):
            layer_type = layer.WhichOneof('layer')
            print(f"层 {i+1}: {layer.name} ({layer_type})")
            
            # 分析不同类型的层
            if layer_type == 'convolution':
                conv = layer.convolution
                print(f"  - 卷积核数量: {conv.outputChannels}")
                print(f"  - 卷积核大小: {list(conv.kernelSize)}")
                print(f"  - 步长: {list(conv.stride)}")
                print(f"  - 填充: {list(conv.valid) if conv.valid else 'same'}")
                
            elif layer_type == 'pooling':
                pool = layer.pooling
                print(f"  - 池化类型: {pool.type}")
                print(f"  - 池化大小: {list(pool.kernelSize)}")
                print(f"  - 步长: {list(pool.stride)}")
                
            elif layer_type == 'activation':
                act = layer.activation
                act_type = act.WhichOneof('NonlinearityType')
                print(f"  - 激活函数: {act_type}")
                
            elif layer_type == 'innerProduct':
                fc = layer.innerProduct
                print(f"  - 输出大小: {fc.outputChannels}")
                
            elif layer_type == 'batchnorm':
                bn = layer.batchnorm
                print(f"  - 通道数: {bn.channels}")
    
    def analyze_neural_network_classifier(self):
        """分析神经网络分类器结构"""
        print("\n神经网络分类器结构:")
        nnc_spec = self.spec.neuralNetworkClassifier
        
        # 分析网络层
        for i, layer in enumerate(nnc_spec.layers):
            layer_type = layer.WhichOneof('layer')
            print(f"层 {i+1}: {layer.name} ({layer_type})")
            
        # 分析分类器信息
        if nnc_spec.HasField('stringClassLabels'):
            print(f"\n类别标签: {list(nnc_spec.stringClassLabels.vector)}")
        elif nnc_spec.HasField('int64ClassLabels'):
            print(f"\n类别标签: {list(nnc_spec.int64ClassLabels.vector)}")

    def analyze_pipeline_classifier(self):
        """分析pipeline分类器结构"""
        print("\nPipeline分类器结构:")
        pipeline_spec = self.spec.pipelineClassifier

        print(f"Pipeline包含 {len(pipeline_spec.pipeline.models)} 个模型:")
        for i, model in enumerate(pipeline_spec.pipeline.models):
            print(f"模型 {i+1}: {model.WhichOneof('Type')}")

            # 如果是神经网络，分析其结构
            if model.HasField('neuralNetwork'):
                print(f"  神经网络层数: {len(model.neuralNetwork.layers)}")
                for j, layer in enumerate(model.neuralNetwork.layers[:5]):  # 只显示前5层
                    layer_type = layer.WhichOneof('layer')
                    print(f"    层 {j+1}: {layer.name} ({layer_type})")
                if len(model.neuralNetwork.layers) > 5:
                    print(f"    ... 还有 {len(model.neuralNetwork.layers) - 5} 层")

        # 分析分类器标签
        try:
            if hasattr(pipeline_spec, 'stringClassLabels') and pipeline_spec.stringClassLabels.vector:
                labels = list(pipeline_spec.stringClassLabels.vector)
                print(f"\n类别标签 ({len(labels)} 个): {labels}")
            elif hasattr(pipeline_spec, 'int64ClassLabels') and pipeline_spec.int64ClassLabels.vector:
                labels = list(pipeline_spec.int64ClassLabels.vector)
                print(f"\n类别标签 ({len(labels)} 个): {labels}")
            else:
                print("\n未找到类别标签信息")
        except Exception as e:
            print(f"\n获取类别标签时出错: {e}")

    def analyze_pipeline(self):
        """分析pipeline结构"""
        print("\nPipeline结构:")
        pipeline_spec = self.spec.pipeline

        print(f"Pipeline包含 {len(pipeline_spec.models)} 个模型:")
        for i, model in enumerate(pipeline_spec.models):
            print(f"模型 {i+1}: {model.WhichOneof('Type')}")
    
    def extract_architecture_info(self):
        """提取架构信息用于重建PyTorch模型"""
        arch_info = {
            'input_shape': None,
            'output_shape': None,
            'num_classes': None,
            'layers': [],
            'model_type': self.spec.WhichOneof('Type')
        }

        # 提取输入输出形状
        if self.spec.description.input:
            input_desc = self.spec.description.input[0]
            if input_desc.type.HasField('multiArrayType'):
                arch_info['input_shape'] = list(input_desc.type.multiArrayType.shape)

        # 对于pipeline分类器，尝试从分类器标签推断类别数
        if self.spec.HasField('pipelineClassifier'):
            pipeline_spec = self.spec.pipelineClassifier
            try:
                if hasattr(pipeline_spec, 'stringClassLabels') and pipeline_spec.stringClassLabels.vector:
                    arch_info['num_classes'] = len(pipeline_spec.stringClassLabels.vector)
                    arch_info['class_labels'] = list(pipeline_spec.stringClassLabels.vector)
                elif hasattr(pipeline_spec, 'int64ClassLabels') and pipeline_spec.int64ClassLabels.vector:
                    arch_info['num_classes'] = len(pipeline_spec.int64ClassLabels.vector)
                    arch_info['class_labels'] = list(pipeline_spec.int64ClassLabels.vector)
                else:
                    arch_info['num_classes'] = 9  # 默认值
            except:
                arch_info['num_classes'] = 9  # 默认值
        elif self.spec.description.output:
            output_desc = self.spec.description.output[0]
            if output_desc.type.HasField('multiArrayType'):
                arch_info['output_shape'] = list(output_desc.type.multiArrayType.shape)
                arch_info['num_classes'] = arch_info['output_shape'][-1] if arch_info['output_shape'] else None

        # 提取层信息
        nn_spec = None
        if self.spec.HasField('neuralNetwork'):
            nn_spec = self.spec.neuralNetwork
        elif self.spec.HasField('neuralNetworkClassifier'):
            nn_spec = self.spec.neuralNetworkClassifier
        elif self.spec.HasField('pipelineClassifier'):
            # 从pipeline中提取第一个神经网络模型
            pipeline_spec = self.spec.pipelineClassifier
            for model in pipeline_spec.pipeline.models:
                if model.HasField('neuralNetwork'):
                    nn_spec = model.neuralNetwork
                    break

        if nn_spec:
            for layer in nn_spec.layers:
                layer_info = {
                    'name': layer.name,
                    'type': layer.WhichOneof('layer')
                }

                if layer_info['type'] == 'convolution':
                    conv = layer.convolution
                    layer_info.update({
                        'out_channels': conv.outputChannels,
                        'kernel_size': list(conv.kernelSize),
                        'stride': list(conv.stride),
                        'padding': 'same' if not conv.valid else 'valid'
                    })
                elif layer_info['type'] == 'innerProduct':
                    fc = layer.innerProduct
                    layer_info['out_features'] = fc.outputChannels

                arch_info['layers'].append(layer_info)

        return arch_info

def create_pytorch_model_from_coreml(arch_info):
    """根据Core ML架构信息创建PyTorch模型"""
    
    class RebuiltDeepInfantModel(nn.Module):
        def __init__(self, arch_info):
            super(RebuiltDeepInfantModel, self).__init__()
            self.arch_info = arch_info
            
            # 根据分析的架构信息构建模型
            # 这里需要根据实际的Core ML模型结构进行调整
            
            # 假设是基于VGGish的架构
            if 'VGGish' in str(arch_info):
                self.features = self._make_vggish_features()
                self.classifier = self._make_vggish_classifier(arch_info.get('num_classes', 9))
            else:
                # 使用原始的DeepInfant架构作为后备
                self.features = self._make_default_features()
                self.classifier = self._make_default_classifier(arch_info.get('num_classes', 9))
        
        def _make_vggish_features(self):
            """创建VGGish风格的特征提取层"""
            return nn.Sequential(
                # Block 1
                nn.Conv2d(1, 64, kernel_size=3, padding=1),
                nn.ReLU(inplace=True),
                nn.MaxPool2d(kernel_size=2, stride=2),
                
                # Block 2
                nn.Conv2d(64, 128, kernel_size=3, padding=1),
                nn.ReLU(inplace=True),
                nn.MaxPool2d(kernel_size=2, stride=2),
                
                # Block 3
                nn.Conv2d(128, 256, kernel_size=3, padding=1),
                nn.ReLU(inplace=True),
                nn.Conv2d(256, 256, kernel_size=3, padding=1),
                nn.ReLU(inplace=True),
                nn.MaxPool2d(kernel_size=2, stride=2),
                
                # Block 4
                nn.Conv2d(256, 512, kernel_size=3, padding=1),
                nn.ReLU(inplace=True),
                nn.Conv2d(512, 512, kernel_size=3, padding=1),
                nn.ReLU(inplace=True),
                nn.MaxPool2d(kernel_size=2, stride=2),
            )
        
        def _make_vggish_classifier(self, num_classes):
            """创建VGGish风格的分类器"""
            # 确保num_classes是有效的整数
            if num_classes is None or num_classes <= 0:
                num_classes = 9  # 默认类别数

            return nn.Sequential(
                nn.AdaptiveAvgPool2d((1, 1)),
                nn.Flatten(),
                nn.Linear(512, 4096),
                nn.ReLU(inplace=True),
                nn.Dropout(0.5),
                nn.Linear(4096, 4096),
                nn.ReLU(inplace=True),
                nn.Dropout(0.5),
                nn.Linear(4096, num_classes)
            )
        
        def _make_default_features(self):
            """创建默认的特征提取层"""
            return nn.Sequential(
                nn.Conv2d(1, 64, kernel_size=3, padding=1),
                nn.BatchNorm2d(64),
                nn.ReLU(),
                nn.MaxPool2d(2),
                
                nn.Conv2d(64, 128, kernel_size=3, padding=1),
                nn.BatchNorm2d(128),
                nn.ReLU(),
                nn.MaxPool2d(2),
                
                nn.Conv2d(128, 256, kernel_size=3, padding=1),
                nn.BatchNorm2d(256),
                nn.ReLU(),
                nn.MaxPool2d(2),
            )
        
        def _make_default_classifier(self, num_classes):
            """创建默认的分类器"""
            # 确保num_classes是有效的整数
            if num_classes is None or num_classes <= 0:
                num_classes = 9  # 默认类别数

            return nn.Sequential(
                nn.AdaptiveAvgPool2d((1, 1)),
                nn.Flatten(),
                nn.Linear(256, 512),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(512, num_classes)
            )
        
        def forward(self, x):
            x = self.features(x)
            x = self.classifier(x)
            return x
    
    return RebuiltDeepInfantModel(arch_info)

def main():
    # 分析两个Core ML模型
    models_to_analyze = [
        "iOS_iPadOS/deepinfant/model/DeepInfant_VGGish.mlmodel",
        "iOS_iPadOS/deepinfant/model/DeepInfant_AFP.mlmodel"
    ]

    for model_path in models_to_analyze:
        if Path(model_path).exists():
            print(f"\n{'='*60}")
            analyzer = CoreMLAnalyzer(model_path)
            arch_info = analyzer.analyze_model()

            # 保存架构信息
            arch_file = f"{Path(model_path).stem}_architecture.json"
            with open(arch_file, 'w', encoding='utf-8') as f:
                json.dump(arch_info, f, indent=2, ensure_ascii=False)
            print(f"\n架构信息已保存到: {arch_file}")

            # 创建PyTorch模型
            pytorch_model = create_pytorch_model_from_coreml(arch_info)

            # 测试模型
            test_input = torch.randn(1, 1, 80, 432)  # 根据实际输入调整
            try:
                output = pytorch_model(test_input)
                print(f"PyTorch模型测试成功，输出形状: {output.shape}")

                # 导出为ONNX
                onnx_path = f"{Path(model_path).stem}_rebuilt.onnx"
                pytorch_model.eval()
                torch.onnx.export(
                    pytorch_model,
                    test_input,
                    onnx_path,
                    export_params=True,
                    opset_version=12,
                    do_constant_folding=True,
                    input_names=['input'],
                    output_names=['output'],
                    dynamic_axes={
                        'input': {0: 'batch_size'},
                        'output': {0: 'batch_size'}
                    }
                )
                print(f"ONNX模型已导出到: {onnx_path}")

            except Exception as e:
                print(f"模型测试失败: {e}")
        else:
            print(f"模型文件不存在: {model_path}")

if __name__ == "__main__":
    main()
