# DeepInfant ONNX 模型使用说明

## 概述

本项目成功从Core ML模型重建并导出了多个ONNX格式的DeepInfant婴儿哭声分类模型。这些模型可以在各种平台和框架中使用，包括Python、C++、JavaScript等。

## 可用模型

### 1. DeepInfant_VGGish_Simple.onnx
- **描述**: 基于VGGish架构的简化模型
- **特点**: 较高的准确性，适合服务器端部署
- **模型大小**: 较大
- **推理速度**: 中等

### 2. DeepInfant_Compact.onnx
- **描述**: 紧凑型模型
- **特点**: 更小的模型大小，更快的推理速度
- **模型大小**: 较小
- **推理速度**: 快

### 3. DeepInfant_VGGish_rebuilt.onnx
- **描述**: 从Core ML VGGish模型重建的版本
- **特点**: 尝试保持与原始Core ML模型的兼容性

### 4. DeepInfant_AFP_rebuilt.onnx
- **描述**: 从Core ML AFP模型重建的版本
- **特点**: 基于音频特征打印技术

## 输入输出规格

### 输入
- **格式**: mel频谱图
- **形状**: `[batch_size, 1, 80, 432]`
- **数据类型**: float32
- **说明**: 
  - `batch_size`: 批次大小（推理时通常为1）
  - `1`: 单通道（灰度）
  - `80`: mel频率bins数量
  - `432`: 时间帧数量

### 输出
- **格式**: 类别概率
- **形状**: `[batch_size, 9]`
- **数据类型**: float32
- **说明**: 9个类别的概率分布

## 类别标签

模型可以识别以下9种婴儿哭声类型：

1. **belly_pain (bp)** - 腹痛
2. **burping (bu)** - 打嗝
3. **cold_hot (ch)** - 冷/热
4. **discomfort (dc)** - 不适
5. **hungry (hu)** - 饥饿
6. **lonely (lo)** - 孤独
7. **scared (sc)** - 害怕
8. **tired (ti)** - 疲倦
9. **unknown (un)** - 未知

## 音频预处理

### 音频要求
- **采样率**: 16000 Hz
- **时长**: 7秒 (112000 samples)
- **格式**: 单声道

### Mel频谱图参数
```python
mel_spectrogram_params = {
    'n_fft': 1024,
    'hop_length': 256,
    'n_mels': 80,
    'fmin': 20,
    'fmax': 8000
}
```

## 使用示例

### Python + ONNX Runtime

```python
import onnxruntime as ort
import numpy as np
import librosa

# 加载模型
session = ort.InferenceSession('DeepInfant_VGGish_Simple.onnx')

# 音频预处理
def preprocess_audio(audio_path):
    # 加载音频
    waveform, sr = librosa.load(audio_path, sr=16000)
    
    # 确保7秒长度
    target_length = 7 * 16000
    if len(waveform) > target_length:
        waveform = waveform[:target_length]
    else:
        waveform = np.pad(waveform, (0, target_length - len(waveform)))
    
    # 生成mel频谱图
    mel_spec = librosa.feature.melspectrogram(
        y=waveform,
        sr=sr,
        n_fft=1024,
        hop_length=256,
        n_mels=80,
        fmin=20,
        fmax=8000
    )
    
    # 转换为对数刻度
    mel_spec = librosa.power_to_db(mel_spec, ref=np.max)
    
    # 添加batch和channel维度
    mel_spec = np.expand_dims(mel_spec, axis=0)  # 添加channel维度
    mel_spec = np.expand_dims(mel_spec, axis=0)  # 添加batch维度
    
    return mel_spec.astype(np.float32)

# 推理
def predict(audio_path):
    # 预处理
    input_data = preprocess_audio(audio_path)
    
    # 运行推理
    outputs = session.run(None, {'mel_spectrogram': input_data})
    probabilities = outputs[0][0]  # 移除batch维度
    
    # 获取预测类别
    predicted_class_idx = np.argmax(probabilities)
    confidence = probabilities[predicted_class_idx]
    
    classes = ['belly_pain', 'burping', 'cold_hot', 'discomfort', 
               'hungry', 'lonely', 'scared', 'tired', 'unknown']
    
    return classes[predicted_class_idx], confidence

# 使用示例
audio_file = 'baby_cry.wav'
predicted_class, confidence = predict(audio_file)
print(f"预测类别: {predicted_class}, 置信度: {confidence:.3f}")
```

### JavaScript + ONNX.js

```javascript
// 加载模型
const session = await ort.InferenceSession.create('DeepInfant_Compact.onnx');

// 预处理和推理函数
async function predictBabyCry(melSpectrogram) {
    // melSpectrogram应该是形状为[1, 1, 80, 432]的Float32Array
    const inputTensor = new ort.Tensor('float32', melSpectrogram, [1, 1, 80, 432]);
    
    const outputs = await session.run({'mel_spectrogram': inputTensor});
    const probabilities = outputs.class_probabilities.data;
    
    const classes = ['belly_pain', 'burping', 'cold_hot', 'discomfort', 
                     'hungry', 'lonely', 'scared', 'tired', 'unknown'];
    
    const maxIndex = probabilities.indexOf(Math.max(...probabilities));
    return {
        class: classes[maxIndex],
        confidence: probabilities[maxIndex]
    };
}
```

## 性能建议

1. **模型选择**:
   - 服务器端部署: 使用 `DeepInfant_VGGish_Simple.onnx`
   - 移动端/边缘设备: 使用 `DeepInfant_Compact.onnx`

2. **批处理**: 如果需要处理多个音频文件，可以增加batch_size以提高效率

3. **硬件加速**: ONNX Runtime支持GPU加速，可以显著提高推理速度

## 注意事项

1. 这些模型是从Core ML模型重建的，可能与原始训练模型存在一些差异
2. 模型性能可能需要在实际数据上进行验证和调优
3. 建议在部署前进行充分的测试

## 文件结构

```
├── DeepInfant_VGGish_Simple.onnx     # VGGish简化模型
├── DeepInfant_Compact.onnx           # 紧凑型模型
├── DeepInfant_VGGish_rebuilt.onnx    # 从Core ML重建的VGGish模型
├── DeepInfant_AFP_rebuilt.onnx       # 从Core ML重建的AFP模型
├── onnx_models_info.json             # 模型详细信息
└── ONNX_MODELS_README.md             # 本说明文档
```

## 技术支持

如果在使用过程中遇到问题，请检查：
1. 输入数据的形状和类型是否正确
2. ONNX Runtime版本是否兼容
3. 音频预处理是否按照规定的参数进行
