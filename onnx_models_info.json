{"models": [{"name": "DeepInfant_VGGish_Simple", "path": "DeepInfant_VGGish_Simple.onnx", "status": "success", "input_shape": [1, 1, 80, 432], "output_shape": [1, 9], "description": "基于VGGish的简化模型，适用于80x432的mel频谱图输入"}, {"name": "DeepInfant_Compact", "path": "DeepInfant_Compact.onnx", "status": "success", "input_shape": [1, 1, 80, 432], "output_shape": [1, 9], "description": "紧凑型模型，更小的模型大小和更快的推理速度"}], "usage_instructions": {"input_format": "mel_spectrogram with shape [batch_size, 1, 80, 432]", "output_format": "class_probabilities with shape [batch_size, 9]", "classes": ["belly_pain (bp)", "burping (bu)", "cold_hot (ch)", "discomfort (dc)", "hungry (hu)", "lonely (lo)", "scared (sc)", "tired (ti)", "unknown (un)"], "preprocessing": {"audio_sample_rate": "16000 Hz", "audio_duration": "7 seconds (112000 samples)", "mel_spectrogram_params": {"n_fft": 1024, "hop_length": 256, "n_mels": 80, "fmin": 20, "fmax": 8000}}}}