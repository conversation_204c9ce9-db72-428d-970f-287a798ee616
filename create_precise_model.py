import torch
import torch.nn as nn
import json
from pathlib import Path

class PreciseDeepInfantModel(nn.Module):
    """基于Core ML模型架构精确重建的PyTorch模型"""
    
    def __init__(self, arch_info):
        super(PreciseDeepInfantModel, self).__init__()
        self.arch_info = arch_info
        
        # 根据实际的Core ML架构构建模型
        self.features = self._build_features_from_arch()
        self.classifier = self._build_classifier()
        
    def _build_features_from_arch(self):
        """根据架构信息构建特征提取层"""
        layers = []
        
        for layer_info in self.arch_info['layers']:
            layer_type = layer_info['type']
            
            if layer_type == 'convolution':
                # 添加卷积层
                in_channels = self._get_previous_channels(layers)
                out_channels = layer_info['out_channels']
                kernel_size = tuple(layer_info['kernel_size'])
                stride = tuple(layer_info['stride'])
                
                # 根据padding类型设置padding
                if layer_info['padding'] == 'valid':
                    padding = 0
                else:  # 'same'
                    padding = (kernel_size[0] // 2, kernel_size[1] // 2)
                
                conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding)
                layers.append(conv)
                
            elif layer_type == 'activation':
                # 添加激活函数（假设是ReLU）
                layers.append(nn.ReLU(inplace=True))
                
            elif layer_type == 'pooling':
                # 添加池化层（假设是MaxPool2d，2x2）
                layers.append(nn.MaxPool2d(kernel_size=2, stride=2))
                
            elif layer_type == 'flatten':
                # 添加展平层
                layers.append(nn.Flatten())
                break  # 特征提取部分结束
        
        return nn.Sequential(*layers)
    
    def _get_previous_channels(self, layers):
        """获取前一层的输出通道数"""
        if not layers:
            return 1  # 输入是单通道（灰度图像）
        
        # 从后往前查找最近的卷积层
        for layer in reversed(layers):
            if isinstance(layer, nn.Conv2d):
                return layer.out_channels
        
        return 1  # 默认值
    
    def _build_classifier(self):
        """构建分类器部分"""
        num_classes = self.arch_info.get('num_classes', 9)
        
        # 根据VGGish架构，最后的特征图大小应该是512通道
        # 这里需要根据实际的特征图大小调整
        return nn.Sequential(
            nn.AdaptiveAvgPool2d((1, 1)),
            nn.Flatten(),
            nn.Linear(512, 4096),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(4096, 4096),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(4096, num_classes)
        )
    
    def forward(self, x):
        x = self.features(x)
        x = self.classifier(x)
        return x

def create_model_from_architecture(arch_file):
    """从架构文件创建精确的PyTorch模型"""
    with open(arch_file, 'r', encoding='utf-8') as f:
        arch_info = json.load(f)
    
    model = PreciseDeepInfantModel(arch_info)
    return model, arch_info

def export_to_onnx(model, arch_info, output_path):
    """导出模型为ONNX格式"""
    model.eval()
    
    # 根据输入形状创建测试输入
    input_shape = arch_info.get('input_shape', [15600])
    
    # 对于音频输入，需要转换为适合CNN的格式
    # 假设输入是mel频谱图：80 mel bins × 432 time frames = 34560
    # 或者是原始音频：15600 samples
    
    if len(input_shape) == 1:
        # 原始音频输入，需要转换为频谱图格式
        if input_shape[0] == 15600:
            # 对应约1秒的16kHz音频，转换为80x20的频谱图
            test_input = torch.randn(1, 1, 80, 20)
        elif input_shape[0] == 80000:
            # 对应5秒的16kHz音频，转换为80x100的频谱图
            test_input = torch.randn(1, 1, 80, 100)
        else:
            # 默认使用80x432的频谱图
            test_input = torch.randn(1, 1, 80, 432)
    else:
        # 多维输入，直接使用
        test_input = torch.randn(1, 1, *input_shape)
    
    try:
        # 测试模型
        output = model(test_input)
        print(f"模型测试成功，输入形状: {test_input.shape}, 输出形状: {output.shape}")
        
        # 导出ONNX
        torch.onnx.export(
            model,
            test_input,
            output_path,
            export_params=True,
            opset_version=12,
            do_constant_folding=True,
            input_names=['input'],
            output_names=['output'],
            dynamic_axes={
                'input': {0: 'batch_size'},
                'output': {0: 'batch_size'}
            }
        )
        print(f"ONNX模型已导出到: {output_path}")
        return True
        
    except Exception as e:
        print(f"导出失败: {e}")
        return False

def main():
    # 处理两个架构文件
    arch_files = [
        "DeepInfant_VGGish_architecture.json",
        "DeepInfant_AFP_architecture.json"
    ]
    
    for arch_file in arch_files:
        if Path(arch_file).exists():
            print(f"\n{'='*60}")
            print(f"处理架构文件: {arch_file}")
            
            # 创建精确模型
            model, arch_info = create_model_from_architecture(arch_file)
            
            # 导出ONNX
            model_name = Path(arch_file).stem.replace('_architecture', '')
            onnx_path = f"{model_name}_precise.onnx"
            
            success = export_to_onnx(model, arch_info, onnx_path)
            
            if success:
                print(f"✅ 成功创建精确的ONNX模型: {onnx_path}")
            else:
                print(f"❌ 创建ONNX模型失败")
        else:
            print(f"架构文件不存在: {arch_file}")

if __name__ == "__main__":
    main()
