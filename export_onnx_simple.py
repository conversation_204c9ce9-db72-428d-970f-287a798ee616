import torch
import torch.nn as nn
import json
from pathlib import Path

class SimpleDeepInfantModel(nn.Module):
    """简化的DeepInfant模型，适合ONNX导出"""
    
    def __init__(self, num_classes=9):
        super(SimpleDeepInfantModel, self).__init__()
        
        # 基于VGGish的简化架构
        self.features = nn.Sequential(
            # Block 1
            nn.Conv2d(1, 64, kernel_size=3, padding=1),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),
            
            # Block 2
            nn.Conv2d(64, 128, kernel_size=3, padding=1),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),
            
            # Block 3
            nn.Conv2d(128, 256, kernel_size=3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, kernel_size=3, padding=1),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),
            
            # Block 4
            nn.Conv2d(256, 512, kernel_size=3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, kernel_size=3, padding=1),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),
        )
        
        self.classifier = nn.Sequential(
            nn.AdaptiveAvgPool2d((1, 1)),
            nn.Flatten(),
            nn.Linear(512, 4096),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(4096, 1024),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(1024, num_classes)
        )
    
    def forward(self, x):
        x = self.features(x)
        x = self.classifier(x)
        return x

class CompactDeepInfantModel(nn.Module):
    """紧凑的DeepInfant模型"""
    
    def __init__(self, num_classes=9):
        super(CompactDeepInfantModel, self).__init__()
        
        self.features = nn.Sequential(
            # 第一层
            nn.Conv2d(1, 32, kernel_size=3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2),
            
            # 第二层
            nn.Conv2d(32, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2),
            
            # 第三层
            nn.Conv2d(64, 128, kernel_size=3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2),
            
            # 第四层
            nn.Conv2d(128, 256, kernel_size=3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool2d((1, 1)),
        )
        
        self.classifier = nn.Sequential(
            nn.Flatten(),
            nn.Linear(256, 128),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(128, num_classes)
        )
    
    def forward(self, x):
        x = self.features(x)
        x = self.classifier(x)
        return x

def export_models_to_onnx():
    """导出多个模型变体为ONNX格式"""
    
    models_to_export = [
        {
            'name': 'DeepInfant_VGGish_Simple',
            'model': SimpleDeepInfantModel(num_classes=9),
            'input_shape': (1, 1, 80, 432),  # mel频谱图格式
            'description': '基于VGGish的简化模型，适用于80x432的mel频谱图输入'
        },
        {
            'name': 'DeepInfant_Compact',
            'model': CompactDeepInfantModel(num_classes=9),
            'input_shape': (1, 1, 80, 432),  # mel频谱图格式
            'description': '紧凑型模型，更小的模型大小和更快的推理速度'
        },

    ]
    
    results = []
    
    for model_info in models_to_export:
        print(f"\n{'='*60}")
        print(f"导出模型: {model_info['name']}")
        print(f"描述: {model_info['description']}")
        
        model = model_info['model']
        model.eval()
        
        # 创建测试输入
        test_input = torch.randn(*model_info['input_shape'])
        
        try:
            # 测试模型前向传播
            with torch.no_grad():
                output = model(test_input)
            print(f"✅ 模型测试成功")
            print(f"   输入形状: {test_input.shape}")
            print(f"   输出形状: {output.shape}")
            
            # 导出ONNX
            onnx_path = f"{model_info['name']}.onnx"
            torch.onnx.export(
                model,
                test_input,
                onnx_path,
                export_params=True,
                opset_version=12,
                do_constant_folding=True,
                input_names=['mel_spectrogram'],
                output_names=['class_probabilities'],
                dynamic_axes={
                    'mel_spectrogram': {0: 'batch_size'},
                    'class_probabilities': {0: 'batch_size'}
                }
            )
            
            print(f"✅ ONNX模型已导出: {onnx_path}")
            
            # 验证ONNX模型
            try:
                import onnx
                onnx_model = onnx.load(onnx_path)
                onnx.checker.check_model(onnx_model)
                print(f"✅ ONNX模型验证通过")

                results.append({
                    'name': model_info['name'],
                    'path': onnx_path,
                    'status': 'success',
                    'input_shape': list(model_info['input_shape']),
                    'output_shape': list(output.shape),
                    'description': model_info['description']
                })

            except Exception as e:
                print(f"⚠️  ONNX模型验证失败: {e}")
                results.append({
                    'name': model_info['name'],
                    'path': onnx_path,
                    'status': 'exported_but_invalid',
                    'input_shape': list(model_info['input_shape']),
                    'output_shape': list(output.shape),
                    'error': str(e)
                })
                
        except Exception as e:
            print(f"❌ 模型导出失败: {e}")
            results.append({
                'name': model_info['name'],
                'status': 'failed',
                'error': str(e)
            })
    
    return results

def create_model_info_file(results):
    """创建模型信息文件"""
    info = {
        'export_timestamp': str(torch.utils.data.get_worker_info()),
        'models': results,
        'usage_instructions': {
            'input_format': 'mel_spectrogram with shape [batch_size, 1, 80, 432]',
            'output_format': 'class_probabilities with shape [batch_size, 9]',
            'classes': [
                'belly_pain (bp)',
                'burping (bu)', 
                'cold_hot (ch)',
                'discomfort (dc)',
                'hungry (hu)',
                'lonely (lo)',
                'scared (sc)',
                'tired (ti)',
                'unknown (un)'
            ],
            'preprocessing': {
                'audio_sample_rate': '16000 Hz',
                'audio_duration': '7 seconds (112000 samples)',
                'mel_spectrogram_params': {
                    'n_fft': 1024,
                    'hop_length': 256,
                    'n_mels': 80,
                    'fmin': 20,
                    'fmax': 8000
                }
            }
        }
    }
    
    with open('onnx_models_info.json', 'w', encoding='utf-8') as f:
        json.dump(info, f, indent=2, ensure_ascii=False)
    
    print(f"\n📋 模型信息已保存到: onnx_models_info.json")

def main():
    print("🚀 开始导出DeepInfant模型为ONNX格式...")
    
    # 导出模型
    results = export_models_to_onnx()
    
    # 创建信息文件
    create_model_info_file(results)
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 导出总结:")
    
    successful = [r for r in results if r['status'] == 'success']
    failed = [r for r in results if r['status'] == 'failed']
    
    print(f"✅ 成功导出: {len(successful)} 个模型")
    for model in successful:
        print(f"   - {model['name']}: {model['path']}")
    
    if failed:
        print(f"❌ 导出失败: {len(failed)} 个模型")
        for model in failed:
            print(f"   - {model['name']}: {model['error']}")
    
    print(f"\n🎉 导出完成！")

if __name__ == "__main__":
    main()
