{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# DeepInfant: Infant Cry Classification\n", "\n", "This notebook demonstrates how to train and evaluate the DeepInfant model for infant cry classification using HuggingFace integration."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import matplotlib.pyplot as plt\n", "from train import DeepInfantModel, DeepInfantDataset\n", "from torch.utils.data import DataLoader\n", "import seaborn as sns\n", "from sklearn.metrics import confusion_matrix\n", "import numpy as np\n", "from train_huggingface import DeepInfantHFModel, DeepInfantHFDataset"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. HuggingFace Dataset Preparation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datasets import load_dataset, Audio\n", "\n", "# Load dataset from HuggingFace (replace with your dataset)\n", "dataset = load_dataset(\"your_username/your_dataset\")\n", "dataset = dataset.cast_column(\"audio\", Audio(sampling_rate=16000))\n", "\n", "print(f\"Training samples: {len(dataset['train'])}\")\n", "print(f\"Validation samples: {len(dataset['validation'])}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Model Configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from transformers import TrainingArguments, Trainer, AutoConfig\n", "\n", "# Configure model\n", "config = AutoConfig.from_pretrained('bert-base-uncased')\n", "config.num_labels = 5  # Number of cry classifications\n", "model = DeepInfantHFModel(config)\n", "\n", "# Define training arguments\n", "training_args = TrainingArguments(\n", "    output_dir=\"./results\",\n", "    num_train_epochs=50,\n", "    per_device_train_batch_size=32,\n", "    per_device_eval_batch_size=32,\n", "    evaluation_strategy=\"epoch\",\n", "    save_strategy=\"epoch\",\n", "    load_deep_infant_at_end=True,\n", "    push_to_hub=True,\n", "    logging_dir='./logs',\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Training"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Prepare datasets\n", "train_dataset = DeepInfantHFDataset(dataset['train'])\n", "eval_dataset = DeepInfantHFDataset(dataset['validation'])\n", "\n", "# Initialize trainer\n", "trainer = Trainer(\n", "    model=model,\n", "    args=training_args,\n", "    train_dataset=train_dataset,\n", "    eval_dataset=eval_dataset,\n", ")\n", "\n", "# Train model\n", "trainer.train()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Evaluation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Evaluate the model\n", "eval_results = trainer.evaluate()\n", "print(\"Evaluation Results:\")\n", "for key, value in eval_results.items():\n", "    print(f\"{key}: {value:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. <PERSON><PERSON> to <PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Push the model to HuggingFace Hub\n", "trainer.push_to_hub()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Test Predictions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from predict import InfantCryPredictor\n", "\n", "# Load the trained model\n", "predictor = InfantCryPredictor('results/checkpoint-deepinfant')\n", "\n", "# Test on a single file\n", "test_file = \"path/to/test/audio.wav\"\n", "label, confidence = predictor.predict(test_file)\n", "print(f\"Prediction: {label} (Confidence: {confidence:.2%})\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}