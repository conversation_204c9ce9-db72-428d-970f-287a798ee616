{"input_shape": [15600], "output_shape": null, "num_classes": 9, "layers": [{"name": "conv1", "type": "convolution", "out_channels": 64, "kernel_size": [3, 3], "stride": [1, 1], "padding": "valid"}, {"name": "conv1__activation__", "type": "activation"}, {"name": "pool1", "type": "pooling"}, {"name": "conv2", "type": "convolution", "out_channels": 128, "kernel_size": [3, 3], "stride": [1, 1], "padding": "valid"}, {"name": "conv2__activation__", "type": "activation"}, {"name": "pool2", "type": "pooling"}, {"name": "conv3_1", "type": "convolution", "out_channels": 256, "kernel_size": [3, 3], "stride": [1, 1], "padding": "valid"}, {"name": "conv3_1__activation__", "type": "activation"}, {"name": "conv3_2", "type": "convolution", "out_channels": 256, "kernel_size": [3, 3], "stride": [1, 1], "padding": "valid"}, {"name": "conv3_2__activation__", "type": "activation"}, {"name": "pool3", "type": "pooling"}, {"name": "conv4_1", "type": "convolution", "out_channels": 512, "kernel_size": [3, 3], "stride": [1, 1], "padding": "valid"}, {"name": "conv4_1__activation__", "type": "activation"}, {"name": "conv4_2", "type": "convolution", "out_channels": 512, "kernel_size": [3, 3], "stride": [1, 1], "padding": "valid"}, {"name": "conv4_2__activation__", "type": "activation"}, {"name": "pool4", "type": "pooling"}, {"name": "flatten", "type": "flatten"}], "model_type": "pipelineClassifier"}