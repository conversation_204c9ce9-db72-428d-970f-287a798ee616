// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 55;
	objects = {

/* Begin PBXBuildFile section */
		A145855727B561A80027A2FF /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = A145855627B561A80027A2FF /* AppDelegate.swift */; };
		A145855927B561A80027A2FF /* SceneDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = A145855827B561A80027A2FF /* SceneDelegate.swift */; };
		A145855B27B561A80027A2FF /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = A145855A27B561A80027A2FF /* ViewController.swift */; };
		A145855E27B561A80027A2FF /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = A145855C27B561A80027A2FF /* Main.storyboard */; };
		A145856027B561A90027A2FF /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A145855F27B561A90027A2FF /* Assets.xcassets */; };
		A145856327B561A90027A2FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = A145856127B561A90027A2FF /* LaunchScreen.storyboard */; };
		A145856C27B561F80027A2FF /* DeepInfant_VGGish.mlmodel in Sources */ = {isa = PBXBuildFile; fileRef = A145856B27B561F80027A2FF /* DeepInfant_VGGish.mlmodel */; };
		A145856E27B5637F0027A2FF /* ResultsObserver.swift in Sources */ = {isa = PBXBuildFile; fileRef = A145856D27B5637F0027A2FF /* ResultsObserver.swift */; };
		A145857027B5947F0027A2FF /* DeepInfant_AFP.mlmodel in Sources */ = {isa = PBXBuildFile; fileRef = A145856F27B5947F0027A2FF /* DeepInfant_AFP.mlmodel */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		A145855327B561A80027A2FF /* DeepInfant.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = DeepInfant.app; sourceTree = BUILT_PRODUCTS_DIR; };
		A145855627B561A80027A2FF /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		A145855827B561A80027A2FF /* SceneDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SceneDelegate.swift; sourceTree = "<group>"; };
		A145855A27B561A80027A2FF /* ViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewController.swift; sourceTree = "<group>"; };
		A145855D27B561A80027A2FF /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		A145855F27B561A90027A2FF /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A145856227B561A90027A2FF /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		A145856427B561A90027A2FF /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A145856B27B561F80027A2FF /* DeepInfant_VGGish.mlmodel */ = {isa = PBXFileReference; lastKnownFileType = file.mlmodel; path = DeepInfant_VGGish.mlmodel; sourceTree = "<group>"; };
		A145856D27B5637F0027A2FF /* ResultsObserver.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ResultsObserver.swift; sourceTree = "<group>"; };
		A145856F27B5947F0027A2FF /* DeepInfant_AFP.mlmodel */ = {isa = PBXFileReference; lastKnownFileType = file.mlmodel; path = DeepInfant_AFP.mlmodel; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A145855027B561A80027A2FF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A145854A27B561A80027A2FF = {
			isa = PBXGroup;
			children = (
				A145855527B561A80027A2FF /* deepinfant */,
				A145855427B561A80027A2FF /* Products */,
			);
			sourceTree = "<group>";
		};
		A145855427B561A80027A2FF /* Products */ = {
			isa = PBXGroup;
			children = (
				A145855327B561A80027A2FF /* DeepInfant.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A145855527B561A80027A2FF /* deepinfant */ = {
			isa = PBXGroup;
			children = (
				A145856A27B561E60027A2FF /* model */,
				A145855627B561A80027A2FF /* AppDelegate.swift */,
				A145855827B561A80027A2FF /* SceneDelegate.swift */,
				A145855A27B561A80027A2FF /* ViewController.swift */,
				A145856D27B5637F0027A2FF /* ResultsObserver.swift */,
				A145855C27B561A80027A2FF /* Main.storyboard */,
				A145855F27B561A90027A2FF /* Assets.xcassets */,
				A145856127B561A90027A2FF /* LaunchScreen.storyboard */,
				A145856427B561A90027A2FF /* Info.plist */,
			);
			path = deepinfant;
			sourceTree = "<group>";
		};
		A145856A27B561E60027A2FF /* model */ = {
			isa = PBXGroup;
			children = (
				A145856B27B561F80027A2FF /* DeepInfant_VGGish.mlmodel */,
				A145856F27B5947F0027A2FF /* DeepInfant_AFP.mlmodel */,
			);
			path = model;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A145855227B561A80027A2FF /* DeepInfant */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A145856727B561A90027A2FF /* Build configuration list for PBXNativeTarget "DeepInfant" */;
			buildPhases = (
				A145854F27B561A80027A2FF /* Sources */,
				A145855027B561A80027A2FF /* Frameworks */,
				A145855127B561A80027A2FF /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = DeepInfant;
			productName = deepinfant;
			productReference = A145855327B561A80027A2FF /* DeepInfant.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A145854B27B561A80027A2FF /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1320;
				LastUpgradeCheck = 1320;
				TargetAttributes = {
					A145855227B561A80027A2FF = {
						CreatedOnToolsVersion = 13.2;
					};
				};
			};
			buildConfigurationList = A145854E27B561A80027A2FF /* Build configuration list for PBXProject "DeepInfant" */;
			compatibilityVersion = "Xcode 13.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = A145854A27B561A80027A2FF;
			productRefGroup = A145855427B561A80027A2FF /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A145855227B561A80027A2FF /* DeepInfant */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A145855127B561A80027A2FF /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A145856327B561A90027A2FF /* LaunchScreen.storyboard in Resources */,
				A145856027B561A90027A2FF /* Assets.xcassets in Resources */,
				A145855E27B561A80027A2FF /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A145854F27B561A80027A2FF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A145857027B5947F0027A2FF /* DeepInfant_AFP.mlmodel in Sources */,
				A145855B27B561A80027A2FF /* ViewController.swift in Sources */,
				A145855727B561A80027A2FF /* AppDelegate.swift in Sources */,
				A145856C27B561F80027A2FF /* DeepInfant_VGGish.mlmodel in Sources */,
				A145855927B561A80027A2FF /* SceneDelegate.swift in Sources */,
				A145856E27B5637F0027A2FF /* ResultsObserver.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		A145855C27B561A80027A2FF /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				A145855D27B561A80027A2FF /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		A145856127B561A90027A2FF /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				A145856227B561A90027A2FF /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		A145856527B561A90027A2FF /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.2;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		A145856627B561A90027A2FF /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.2;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		A145856827B561A90027A2FF /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = AMJ453QYKS;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = deepinfant/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = DeepInfant;
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "Mic is needed";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = skytells.research.deepinfant;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		A145856927B561A90027A2FF /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = AMJ453QYKS;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = deepinfant/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = DeepInfant;
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "Mic is needed";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = skytells.research.deepinfant;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A145854E27B561A80027A2FF /* Build configuration list for PBXProject "DeepInfant" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A145856527B561A90027A2FF /* Debug */,
				A145856627B561A90027A2FF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A145856727B561A90027A2FF /* Build configuration list for PBXNativeTarget "DeepInfant" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A145856827B561A90027A2FF /* Debug */,
				A145856927B561A90027A2FF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = A145854B27B561A80027A2FF /* Project object */;
}
